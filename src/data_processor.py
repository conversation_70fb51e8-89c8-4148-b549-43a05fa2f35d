"""
数据处理模块
用于清理和结构化从PDF中提取的数据
"""

import pandas as pd
import re
from typing import List, Dict, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        """初始化数据处理器"""
        pass
    
    def clean_cell_data(self, cell: str) -> str:
        """
        清理单元格数据
        
        Args:
            cell: 原始单元格数据
            
        Returns:
            清理后的数据
        """
        if not cell or not isinstance(cell, str):
            return ""
        
        # 移除多余的空白字符
        cell = re.sub(r'\s+', ' ', cell.strip())
        
        # 移除特殊字符（保留中文、英文、数字、常用标点）
        cell = re.sub(r'[^\u4e00-\u9fff\w\s\.\,\-\(\)\[\]\/\%\$\￥\：\；\。\，]', '', cell)
        
        return cell.strip()
    
    def detect_header_row(self, table_data: List[List[str]]) -> int:
        """
        检测表格的标题行
        
        Args:
            table_data: 表格数据
            
        Returns:
            标题行的索引，如果没有找到返回0
        """
        if not table_data or len(table_data) < 2:
            return 0
        
        # 检查前几行，找到最可能是标题的行
        for i, row in enumerate(table_data[:3]):
            # 如果行中包含较多非空单元格，可能是标题行
            non_empty_cells = sum(1 for cell in row if cell and cell.strip())
            if non_empty_cells >= len(row) * 0.5:  # 至少50%的单元格非空
                return i
        
        return 0
    
    def process_table(self, table_data: List[List[str]], 
                     page_num: int, table_index: int) -> pd.DataFrame:
        """
        处理单个表格数据
        
        Args:
            table_data: 原始表格数据
            page_num: 页面编号
            table_index: 表格索引
            
        Returns:
            处理后的DataFrame
        """
        if not table_data:
            return pd.DataFrame()
        
        # 清理所有单元格数据
        cleaned_data = []
        for row in table_data:
            cleaned_row = [self.clean_cell_data(cell) for cell in row]
            cleaned_data.append(cleaned_row)
        
        # 移除完全空白的行
        cleaned_data = [row for row in cleaned_data if any(cell for cell in row)]
        
        if not cleaned_data:
            return pd.DataFrame()
        
        # 检测标题行
        header_row_index = self.detect_header_row(cleaned_data)
        
        # 确保所有行的列数一致
        max_cols = max(len(row) for row in cleaned_data)
        for row in cleaned_data:
            while len(row) < max_cols:
                row.append("")
        
        # 创建DataFrame
        if header_row_index < len(cleaned_data):
            headers = cleaned_data[header_row_index]
            data_rows = cleaned_data[header_row_index + 1:]
            
            # 处理重复的列名
            headers = self._handle_duplicate_headers(headers)
            
            df = pd.DataFrame(data_rows, columns=headers)
        else:
            # 如果没有明确的标题行，使用默认列名
            headers = [f"列{i+1}" for i in range(max_cols)]
            df = pd.DataFrame(cleaned_data, columns=headers)
        
        # 添加元数据列
        df['_页面'] = page_num
        df['_表格'] = table_index
        
        # 移除完全空白的行
        df = df.dropna(how='all', subset=[col for col in df.columns if not col.startswith('_')])
        
        logger.info(f"处理表格 (页面{page_num}, 表格{table_index}): {len(df)} 行 x {len(df.columns)} 列")
        
        return df
    
    def _handle_duplicate_headers(self, headers: List[str]) -> List[str]:
        """
        处理重复的列标题
        
        Args:
            headers: 原始标题列表
            
        Returns:
            处理后的标题列表
        """
        seen = {}
        result = []
        
        for header in headers:
            if not header or header.strip() == "":
                header = "未命名列"
            
            original_header = header
            counter = 1
            
            while header in seen:
                header = f"{original_header}_{counter}"
                counter += 1
            
            seen[header] = True
            result.append(header)
        
        return result
    
    def merge_tables(self, dataframes: List[pd.DataFrame]) -> pd.DataFrame:
        """
        合并多个表格
        
        Args:
            dataframes: DataFrame列表
            
        Returns:
            合并后的DataFrame
        """
        if not dataframes:
            return pd.DataFrame()
        
        if len(dataframes) == 1:
            return dataframes[0]
        
        # 尝试智能合并具有相似结构的表格
        merged_dfs = []
        
        # 按列数分组
        by_columns = {}
        for df in dataframes:
            col_count = len([col for col in df.columns if not col.startswith('_')])
            if col_count not in by_columns:
                by_columns[col_count] = []
            by_columns[col_count].append(df)
        
        # 合并每组
        for col_count, dfs in by_columns.items():
            if len(dfs) == 1:
                merged_dfs.append(dfs[0])
            else:
                # 尝试合并相似的表格
                try:
                    merged = pd.concat(dfs, ignore_index=True, sort=False)
                    merged_dfs.append(merged)
                except Exception as e:
                    logger.warning(f"无法合并 {len(dfs)} 个表格: {e}")
                    merged_dfs.extend(dfs)
        
        # 如果只有一个合并后的表格，直接返回
        if len(merged_dfs) == 1:
            return merged_dfs[0]
        
        # 否则创建一个包含所有数据的大表格
        all_data = []
        for i, df in enumerate(merged_dfs):
            # 添加表格分隔信息
            separator_row = pd.DataFrame([[""] * len(df.columns)], columns=df.columns)
            separator_row.iloc[0, 0] = f"=== 表格组 {i+1} ==="
            
            combined = pd.concat([separator_row, df], ignore_index=True)
            all_data.append(combined)
        
        final_df = pd.concat(all_data, ignore_index=True, sort=False)
        return final_df
    
    def process_all_tables(self, tables_info: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        处理所有表格数据
        
        Args:
            tables_info: 包含表格信息的字典列表
            
        Returns:
            处理后的合并DataFrame
        """
        if not tables_info:
            logger.warning("没有找到表格数据")
            return pd.DataFrame()
        
        processed_dfs = []
        
        for table_info in tables_info:
            try:
                df = self.process_table(
                    table_info['data'],
                    table_info['page'],
                    table_info['table_index']
                )
                
                if not df.empty:
                    processed_dfs.append(df)
                    
            except Exception as e:
                logger.error(f"处理表格时出错 (页面{table_info['page']}, "
                           f"表格{table_info['table_index']}): {e}")
        
        if not processed_dfs:
            logger.warning("没有成功处理任何表格")
            return pd.DataFrame()
        
        # 合并所有处理后的表格
        final_df = self.merge_tables(processed_dfs)
        
        logger.info(f"最终处理结果: {len(final_df)} 行 x {len(final_df.columns)} 列")
        
        return final_df
    
    def analyze_data_types(self, df: pd.DataFrame) -> Dict[str, str]:
        """
        分析数据类型
        
        Args:
            df: DataFrame
            
        Returns:
            列名到数据类型的映射
        """
        type_mapping = {}
        
        for col in df.columns:
            if col.startswith('_'):
                continue
                
            # 尝试识别数据类型
            sample_values = df[col].dropna().astype(str).str.strip()
            sample_values = sample_values[sample_values != ""]
            
            if len(sample_values) == 0:
                type_mapping[col] = "文本"
                continue
            
            # 检查是否为数字
            numeric_count = 0
            for value in sample_values:
                if re.match(r'^-?\d+\.?\d*$', value.replace(',', '')):
                    numeric_count += 1
            
            if numeric_count / len(sample_values) > 0.8:
                type_mapping[col] = "数字"
            elif any(keyword in col.lower() for keyword in ['日期', 'date', '时间', 'time']):
                type_mapping[col] = "日期"
            else:
                type_mapping[col] = "文本"
        
        return type_mapping
