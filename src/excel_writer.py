"""
Excel导出模块
用于将处理后的数据导出为Excel格式
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from typing import Dict, Any, Optional
import logging
from datetime import datetime
import os

logger = logging.getLogger(__name__)


class ExcelWriter:
    """Excel文件写入器"""
    
    def __init__(self, output_path: str):
        """
        初始化Excel写入器
        
        Args:
            output_path: 输出Excel文件路径
        """
        self.output_path = output_path
        self.workbook = None
        
    def create_workbook(self):
        """创建新的工作簿"""
        self.workbook = openpyxl.Workbook()
        # 删除默认的工作表
        if 'Sheet' in self.workbook.sheetnames:
            self.workbook.remove(self.workbook['Sheet'])
    
    def add_summary_sheet(self, pdf_info: Dict[str, Any], 
                         tables_count: int, total_rows: int):
        """
        添加摘要工作表
        
        Args:
            pdf_info: PDF文件信息
            tables_count: 表格数量
            total_rows: 总行数
        """
        if not self.workbook:
            self.create_workbook()
        
        ws = self.workbook.create_sheet("转换摘要", 0)
        
        # 设置标题
        ws['A1'] = "PDF转Excel转换报告"
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        ws['A1'].font = Font(size=16, bold=True, color="FFFFFF")
        
        # 基本信息
        info_data = [
            ["转换时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ["源文件", os.path.basename(pdf_info.get('path', ''))],
            ["PDF页数", pdf_info.get('pages', 0)],
            ["提取表格数", tables_count],
            ["总数据行数", total_rows],
            ["", ""],
            ["PDF信息", ""],
            ["标题", pdf_info.get('title', '无')],
            ["作者", pdf_info.get('author', '无')],
            ["创建日期", pdf_info.get('creation_date', '无')],
        ]
        
        for i, (key, value) in enumerate(info_data, start=3):
            ws[f'A{i}'] = key
            ws[f'B{i}'] = value
            
            if key in ["转换时间", "PDF信息"]:
                ws[f'A{i}'].font = Font(bold=True)
        
        # 设置列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 30
        
        # 合并标题单元格
        ws.merge_cells('A1:B1')
    
    def add_data_sheet(self, df: pd.DataFrame, sheet_name: str = "数据"):
        """
        添加数据工作表
        
        Args:
            df: 要写入的DataFrame
            sheet_name: 工作表名称
        """
        if not self.workbook:
            self.create_workbook()
        
        if df.empty:
            logger.warning("DataFrame为空，跳过创建数据工作表")
            return
        
        # 创建工作表
        ws = self.workbook.create_sheet(sheet_name)
        
        # 写入数据
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)
        
        # 设置样式
        self._apply_table_style(ws, len(df) + 1, len(df.columns))
        
        logger.info(f"已创建工作表 '{sheet_name}': {len(df)} 行 x {len(df.columns)} 列")
    
    def add_separate_table_sheets(self, tables_info: list, processed_dfs: list):
        """
        为每个表格创建单独的工作表
        
        Args:
            tables_info: 表格信息列表
            processed_dfs: 处理后的DataFrame列表
        """
        if not self.workbook:
            self.create_workbook()
        
        for i, (table_info, df) in enumerate(zip(tables_info, processed_dfs)):
            if df.empty:
                continue
                
            sheet_name = f"页面{table_info['page']}_表格{table_info['table_index']}"
            
            # 确保工作表名称不超过31个字符（Excel限制）
            if len(sheet_name) > 31:
                sheet_name = f"P{table_info['page']}_T{table_info['table_index']}"
            
            try:
                ws = self.workbook.create_sheet(sheet_name)
                
                # 写入数据
                for r in dataframe_to_rows(df, index=False, header=True):
                    ws.append(r)
                
                # 设置样式
                self._apply_table_style(ws, len(df) + 1, len(df.columns))
                
                logger.info(f"已创建工作表 '{sheet_name}': {len(df)} 行 x {len(df.columns)} 列")
                
            except Exception as e:
                logger.error(f"创建工作表 '{sheet_name}' 时出错: {e}")
    
    def _apply_table_style(self, worksheet, max_row: int, max_col: int):
        """
        应用表格样式
        
        Args:
            worksheet: 工作表对象
            max_row: 最大行数
            max_col: 最大列数
        """
        # 定义样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        center_alignment = Alignment(horizontal='center', vertical='center')
        
        # 应用标题行样式
        for col in range(1, max_col + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = thin_border
            cell.alignment = center_alignment
        
        # 应用数据行样式
        for row in range(2, max_row + 1):
            for col in range(1, max_col + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.border = thin_border
                
                # 交替行颜色
                if row % 2 == 0:
                    cell.fill = PatternFill(start_color="F2F2F2", end_color="F2F2F2", fill_type="solid")
        
        # 自动调整列宽
        for col in range(1, max_col + 1):
            column_letter = openpyxl.utils.get_column_letter(col)
            max_length = 0
            
            for row in range(1, min(max_row + 1, 100)):  # 限制检查前100行以提高性能
                cell_value = worksheet.cell(row=row, column=col).value
                if cell_value:
                    max_length = max(max_length, len(str(cell_value)))
            
            # 设置列宽，最小8，最大50
            adjusted_width = min(max(max_length + 2, 8), 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def save_excel(self, df: pd.DataFrame, pdf_info: Dict[str, Any], 
                   tables_info: Optional[list] = None, 
                   create_separate_sheets: bool = True) -> bool:
        """
        保存Excel文件
        
        Args:
            df: 主要数据DataFrame
            pdf_info: PDF文件信息
            tables_info: 表格信息列表
            create_separate_sheets: 是否为每个表格创建单独工作表
            
        Returns:
            是否保存成功
        """
        try:
            self.create_workbook()
            
            # 添加摘要工作表
            tables_count = len(tables_info) if tables_info else 0
            self.add_summary_sheet(pdf_info, tables_count, len(df))
            
            # 添加合并数据工作表
            if not df.empty:
                self.add_data_sheet(df, "所有数据")
            
            # 如果需要，为每个表格创建单独工作表
            if create_separate_sheets and tables_info:
                # 这里需要重新处理每个表格以获得单独的DataFrame
                # 为简化，我们跳过这个功能，只创建合并数据表
                pass
            
            # 保存文件
            self.workbook.save(self.output_path)
            logger.info(f"Excel文件已保存到: {self.output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存Excel文件时出错: {e}")
            return False
    
    def save_simple_excel(self, df: pd.DataFrame) -> bool:
        """
        保存简单的Excel文件（仅数据）
        
        Args:
            df: 要保存的DataFrame
            
        Returns:
            是否保存成功
        """
        try:
            if df.empty:
                logger.warning("DataFrame为空，无法保存")
                return False
            
            # 使用pandas直接保存，更简单快速
            with pd.ExcelWriter(self.output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='数据', index=False)
            
            logger.info(f"Excel文件已保存到: {self.output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存Excel文件时出错: {e}")
            return False


def save_dataframe_to_excel(df: pd.DataFrame, output_path: str, 
                           pdf_info: Optional[Dict[str, Any]] = None,
                           simple_mode: bool = False) -> bool:
    """
    便捷函数：将DataFrame保存为Excel文件
    
    Args:
        df: 要保存的DataFrame
        output_path: 输出文件路径
        pdf_info: PDF文件信息（可选）
        simple_mode: 是否使用简单模式
        
    Returns:
        是否保存成功
    """
    writer = ExcelWriter(output_path)
    
    if simple_mode or not pdf_info:
        return writer.save_simple_excel(df)
    else:
        return writer.save_excel(df, pdf_info)
