"""
PDF读取模块
用于从PDF文件中提取文本和表格数据
"""

import pdfplumber
import pandas as pd
from typing import List, Dict, Any, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PDFReader:
    """PDF文件读取器"""
    
    def __init__(self, pdf_path: str):
        """
        初始化PDF读取器
        
        Args:
            pdf_path: PDF文件路径
        """
        self.pdf_path = pdf_path
        self.pdf = None
        
    def __enter__(self):
        """上下文管理器入口"""
        self.pdf = pdfplumber.open(self.pdf_path)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self.pdf:
            self.pdf.close()
    
    def get_page_count(self) -> int:
        """获取PDF页数"""
        return len(self.pdf.pages) if self.pdf else 0
    
    def extract_text_from_page(self, page_num: int) -> str:
        """
        从指定页面提取文本
        
        Args:
            page_num: 页面编号（从0开始）
            
        Returns:
            页面文本内容
        """
        if not self.pdf or page_num >= len(self.pdf.pages):
            return ""
            
        page = self.pdf.pages[page_num]
        return page.extract_text() or ""
    
    def extract_tables_from_page(self, page_num: int) -> List[List[List[str]]]:
        """
        从指定页面提取表格
        
        Args:
            page_num: 页面编号（从0开始）
            
        Returns:
            表格数据列表，每个表格是一个二维列表
        """
        if not self.pdf or page_num >= len(self.pdf.pages):
            return []
            
        page = self.pdf.pages[page_num]
        tables = page.extract_tables()
        
        # 清理表格数据，移除空值
        cleaned_tables = []
        for table in tables:
            cleaned_table = []
            for row in table:
                cleaned_row = [cell.strip() if cell else "" for cell in row]
                cleaned_table.append(cleaned_row)
            cleaned_tables.append(cleaned_table)
            
        return cleaned_tables
    
    def extract_all_text(self) -> str:
        """
        提取所有页面的文本
        
        Returns:
            所有页面的文本内容
        """
        all_text = []
        for i in range(self.get_page_count()):
            text = self.extract_text_from_page(i)
            if text:
                all_text.append(f"=== 第 {i+1} 页 ===\n{text}")
        
        return "\n\n".join(all_text)
    
    def extract_all_tables(self) -> List[Dict[str, Any]]:
        """
        提取所有页面的表格
        
        Returns:
            包含表格信息的字典列表，每个字典包含页面号和表格数据
        """
        all_tables = []
        
        for page_num in range(self.get_page_count()):
            tables = self.extract_tables_from_page(page_num)
            
            for table_index, table in enumerate(tables):
                if table and len(table) > 0:  # 确保表格不为空
                    table_info = {
                        'page': page_num + 1,
                        'table_index': table_index + 1,
                        'data': table,
                        'rows': len(table),
                        'cols': len(table[0]) if table else 0
                    }
                    all_tables.append(table_info)
                    
        logger.info(f"从PDF中提取到 {len(all_tables)} 个表格")
        return all_tables
    
    def get_pdf_info(self) -> Dict[str, Any]:
        """
        获取PDF文件信息
        
        Returns:
            PDF文件的基本信息
        """
        if not self.pdf:
            return {}
            
        metadata = self.pdf.metadata or {}
        
        return {
            'path': self.pdf_path,
            'pages': self.get_page_count(),
            'title': metadata.get('Title', ''),
            'author': metadata.get('Author', ''),
            'subject': metadata.get('Subject', ''),
            'creator': metadata.get('Creator', ''),
            'producer': metadata.get('Producer', ''),
            'creation_date': metadata.get('CreationDate', ''),
            'modification_date': metadata.get('ModDate', '')
        }


def test_pdf_reader(pdf_path: str):
    """
    测试PDF读取器功能
    
    Args:
        pdf_path: PDF文件路径
    """
    try:
        with PDFReader(pdf_path) as reader:
            # 获取PDF信息
            info = reader.get_pdf_info()
            print(f"PDF信息: {info}")
            
            # 提取所有表格
            tables = reader.extract_all_tables()
            print(f"找到 {len(tables)} 个表格")
            
            # 显示每个表格的基本信息
            for table in tables:
                print(f"页面 {table['page']}, 表格 {table['table_index']}: "
                      f"{table['rows']} 行 x {table['cols']} 列")
                
    except Exception as e:
        logger.error(f"读取PDF文件时出错: {e}")


if __name__ == "__main__":
    # 测试代码
    test_pdf_reader("谷付荣.pdf")
