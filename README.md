# PDF to Excel Converter

一个功能强大的Python工具，用于将PDF文件中的表格数据转换为Excel格式。

## 功能特性

- 🔍 **智能表格检测**: 自动识别和提取PDF中的表格结构
- 📊 **数据清理**: 自动清理和格式化提取的数据
- 📈 **Excel导出**: 支持多种Excel导出格式，包含样式和格式
- 🎯 **多页面支持**: 处理多页PDF文件中的所有表格
- 💻 **命令行界面**: 简单易用的命令行工具
- 🔧 **编程接口**: 提供Python API供其他程序调用

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 基本使用

```bash
# 转换PDF文件（自动生成输出文件名）
python main.py -i "谷付荣.pdf"

# 指定输出文件名
python main.py -i "谷付荣.pdf" -o "转换结果.xlsx"

# 使用简单模式（仅数据，无格式）
python main.py -i "谷付荣.pdf" -o "结果.xlsx" --simple

# 显示详细日志
python main.py -i "谷付荣.pdf" -v
```

### 3. 编程接口

```python
from main import convert_file

# 转换PDF文件
success = convert_file("谷付荣.pdf", "output.xlsx")
if success:
    print("转换成功!")
```

## 命令行参数

| 参数 | 简写 | 说明 | 必需 |
|------|------|------|------|
| `--input` | `-i` | 输入PDF文件路径 | ✓ |
| `--output` | `-o` | 输出Excel文件路径 | ✗ |
| `--simple` | `-s` | 使用简单模式 | ✗ |
| `--verbose` | `-v` | 显示详细日志 | ✗ |

## 项目结构

```
├── src/                    # 源代码目录
│   ├── __init__.py        # 包初始化文件
│   ├── pdf_reader.py      # PDF读取和解析模块
│   ├── data_processor.py  # 数据清理和处理模块
│   └── excel_writer.py    # Excel导出和格式化模块
├── main.py                # 主程序入口
├── test_converter.py      # 测试脚本
├── requirements.txt       # 项目依赖
├── README.md             # 项目说明
└── 谷付荣.pdf            # 示例PDF文件
```

## 测试

### 运行测试

```bash
# 手动测试（推荐）
python test_converter.py --manual

# 单元测试
python test_converter.py --unittest
```

## 支持的PDF格式

- ✅ 标准表格格式
- ✅ 多页面PDF文件
- ✅ 中文内容
- ✅ 混合文本和表格
- ⚠️ 复杂布局（可能需要手动调整）
- ⚠️ 图片中的表格（需要OCR功能）

## 输出格式

转换后的Excel文件包含：

1. **转换摘要工作表**: 包含转换信息和PDF元数据
2. **所有数据工作表**: 合并所有表格的数据
3. **格式化样式**: 标题行高亮、边框、交替行颜色
4. **自动列宽**: 根据内容自动调整列宽

## 故障排除

### 常见问题

1. **找不到表格数据**
   - 检查PDF是否包含真实的表格（而非图片）
   - 尝试使用其他PDF查看器确认表格结构

2. **转换结果不准确**
   - PDF表格结构复杂，可能需要手动调整
   - 查看日志文件 `pdf_to_excel.log` 获取详细信息

3. **依赖安装失败**
   - 确保Python版本 >= 3.7
   - 使用虚拟环境避免依赖冲突

### 日志文件

程序运行时会生成 `pdf_to_excel.log` 日志文件，包含详细的执行信息和错误信息。

## 技术栈

- **PDF处理**: pdfplumber, PyPDF2
- **数据处理**: pandas, numpy
- **Excel导出**: openpyxl
- **命令行界面**: click
- **表格检测**: tabula-py, camelot-py

## 注意事项

- 确保PDF文件包含可提取的表格数据（非图片格式）
- 复杂的PDF布局可能需要手动调整参数
- 建议在虚拟环境中运行此项目
- 大型PDF文件可能需要较长处理时间

## 许可证

本项目仅供学习和研究使用。
