#!/usr/bin/env python3
"""
测试PDF转Excel转换器
"""

import os
import sys
import unittest
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pdf_reader import PDFReader
from data_processor import DataProcessor
from excel_writer import ExcelWriter
from main import PDFToExcelConverter


class TestPDFToExcelConverter(unittest.TestCase):
    """PDF转Excel转换器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_pdf = "谷付荣.pdf"
        self.test_output = "test_output.xlsx"
        self.converter = PDFToExcelConverter()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_output):
            os.remove(self.test_output)
    
    def test_pdf_exists(self):
        """测试PDF文件是否存在"""
        self.assertTrue(os.path.exists(self.test_pdf), f"PDF文件不存在: {self.test_pdf}")
    
    def test_pdf_reader(self):
        """测试PDF读取功能"""
        if not os.path.exists(self.test_pdf):
            self.skipTest(f"PDF文件不存在: {self.test_pdf}")
        
        with PDFReader(self.test_pdf) as reader:
            # 测试基本信息
            info = reader.get_pdf_info()
            self.assertIsInstance(info, dict)
            self.assertGreater(info.get('pages', 0), 0)
            
            # 测试文本提取
            text = reader.extract_text_from_page(0)
            self.assertIsInstance(text, str)
            
            # 测试表格提取
            tables = reader.extract_all_tables()
            self.assertIsInstance(tables, list)
            
            print(f"PDF信息: {info}")
            print(f"找到 {len(tables)} 个表格")
    
    def test_data_processor(self):
        """测试数据处理功能"""
        processor = DataProcessor()
        
        # 测试单元格清理
        test_data = "  测试数据  \n\r"
        cleaned = processor.clean_cell_data(test_data)
        self.assertEqual(cleaned, "测试数据")
        
        # 测试表格处理
        test_table = [
            ["姓名", "年龄", "城市"],
            ["张三", "25", "北京"],
            ["李四", "30", "上海"]
        ]
        
        df = processor.process_table(test_table, 1, 1)
        self.assertFalse(df.empty)
        self.assertEqual(len(df), 2)  # 2行数据（不包括标题）
        self.assertEqual(len(df.columns), 5)  # 3列数据 + 2列元数据
    
    def test_excel_writer(self):
        """测试Excel写入功能"""
        import pandas as pd
        
        # 创建测试数据
        test_df = pd.DataFrame({
            '姓名': ['张三', '李四'],
            '年龄': [25, 30],
            '城市': ['北京', '上海']
        })
        
        writer = ExcelWriter(self.test_output)
        success = writer.save_simple_excel(test_df)
        
        self.assertTrue(success)
        self.assertTrue(os.path.exists(self.test_output))
    
    def test_full_conversion(self):
        """测试完整转换流程"""
        if not os.path.exists(self.test_pdf):
            self.skipTest(f"PDF文件不存在: {self.test_pdf}")
        
        success = self.converter.convert(self.test_pdf, self.test_output, simple_mode=True)
        
        if success:
            self.assertTrue(os.path.exists(self.test_output))
            print(f"转换成功，输出文件: {self.test_output}")
        else:
            print("转换失败，可能PDF中没有表格数据")


def run_manual_test():
    """手动测试函数"""
    print("=== PDF转Excel转换器手动测试 ===")
    
    pdf_file = "谷付荣.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"错误: PDF文件不存在 - {pdf_file}")
        return
    
    print(f"测试文件: {pdf_file}")
    
    try:
        # 测试PDF读取
        print("\n1. 测试PDF读取...")
        with PDFReader(pdf_file) as reader:
            info = reader.get_pdf_info()
            print(f"   PDF页数: {info.get('pages', 0)}")
            print(f"   PDF标题: {info.get('title', '无')}")
            
            tables = reader.extract_all_tables()
            print(f"   找到表格: {len(tables)} 个")
            
            for i, table in enumerate(tables):
                print(f"   表格 {i+1}: 页面{table['page']}, {table['rows']}行 x {table['cols']}列")
        
        # 测试完整转换
        print("\n2. 测试完整转换...")
        converter = PDFToExcelConverter()
        output_file = "谷付荣_转换结果.xlsx"
        
        success = converter.convert(pdf_file, output_file, simple_mode=False)
        
        if success:
            print(f"   ✓ 转换成功: {output_file}")
            
            # 显示文件信息
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"   文件大小: {file_size:,} 字节")
        else:
            print("   ✗ 转换失败")
    
    except Exception as e:
        print(f"测试过程中出错: {e}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="PDF转Excel转换器测试")
    parser.add_argument('--manual', action='store_true', help='运行手动测试')
    parser.add_argument('--unittest', action='store_true', help='运行单元测试')
    
    args = parser.parse_args()
    
    if args.manual:
        run_manual_test()
    elif args.unittest:
        unittest.main(argv=[''])
    else:
        # 默认运行手动测试
        run_manual_test()
