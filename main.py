#!/usr/bin/env python3
"""
PDF to Excel Converter
主程序入口文件
"""

import click
import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pdf_reader import PDFReader
from data_processor import DataProcessor
from excel_writer import save_dataframe_to_excel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('pdf_to_excel.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


class PDFToExcelConverter:
    """PDF到Excel转换器"""
    
    def __init__(self):
        self.pdf_reader = None
        self.data_processor = DataProcessor()
    
    def convert(self, pdf_path: str, output_path: str, simple_mode: bool = False) -> bool:
        """
        执行PDF到Excel的转换
        
        Args:
            pdf_path: PDF文件路径
            output_path: 输出Excel文件路径
            simple_mode: 是否使用简单模式
            
        Returns:
            转换是否成功
        """
        try:
            logger.info(f"开始转换: {pdf_path} -> {output_path}")
            
            # 检查输入文件
            if not os.path.exists(pdf_path):
                logger.error(f"PDF文件不存在: {pdf_path}")
                return False
            
            # 创建输出目录
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 读取PDF文件
            with PDFReader(pdf_path) as reader:
                # 获取PDF信息
                pdf_info = reader.get_pdf_info()
                logger.info(f"PDF信息: {pdf_info['pages']} 页")
                
                # 提取所有表格
                tables_info = reader.extract_all_tables()
                
                if not tables_info:
                    logger.warning("未在PDF中找到表格数据")
                    
                    # 尝试提取文本并创建简单表格
                    all_text = reader.extract_all_text()
                    if all_text:
                        # 创建包含文本的简单DataFrame
                        import pandas as pd
                        text_df = pd.DataFrame({
                            '页面': [f"第{i+1}页" for i in range(pdf_info['pages'])],
                            '内容': [reader.extract_text_from_page(i) for i in range(pdf_info['pages'])]
                        })
                        
                        success = save_dataframe_to_excel(
                            text_df, output_path, pdf_info, simple_mode
                        )
                        
                        if success:
                            logger.info("已将PDF文本内容导出为Excel")
                        return success
                    else:
                        logger.error("PDF中没有可提取的内容")
                        return False
                
                # 处理表格数据
                logger.info(f"开始处理 {len(tables_info)} 个表格")
                processed_df = self.data_processor.process_all_tables(tables_info)
                
                if processed_df.empty:
                    logger.error("处理后的数据为空")
                    return False
                
                # 保存到Excel
                success = save_dataframe_to_excel(
                    processed_df, output_path, pdf_info, simple_mode
                )
                
                if success:
                    logger.info(f"转换完成! 输出文件: {output_path}")
                    logger.info(f"数据统计: {len(processed_df)} 行 x {len(processed_df.columns)} 列")
                
                return success
                
        except Exception as e:
            logger.error(f"转换过程中出错: {e}")
            return False


@click.command()
@click.option('--input', '-i', 'input_path', required=True, 
              help='输入PDF文件路径')
@click.option('--output', '-o', 'output_path', 
              help='输出Excel文件路径（可选，默认为PDF文件名.xlsx）')
@click.option('--simple', '-s', is_flag=True, 
              help='使用简单模式（仅保存数据，不包含格式）')
@click.option('--verbose', '-v', is_flag=True, 
              help='显示详细日志信息')
def main(input_path, output_path, simple, verbose):
    """
    PDF to Excel Converter
    
    将PDF文件中的表格数据转换为Excel格式
    
    示例:
        python main.py -i "谷付荣.pdf" -o "output.xlsx"
        python main.py -i "谷付荣.pdf" --simple
    """
    
    # 设置日志级别
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 处理输入路径
    input_path = os.path.abspath(input_path)
    
    # 处理输出路径
    if not output_path:
        # 默认输出路径：与输入文件同目录，扩展名改为.xlsx
        input_name = os.path.splitext(os.path.basename(input_path))[0]
        output_path = os.path.join(
            os.path.dirname(input_path), 
            f"{input_name}_转换结果.xlsx"
        )
    else:
        output_path = os.path.abspath(output_path)
    
    # 显示转换信息
    click.echo(f"输入文件: {input_path}")
    click.echo(f"输出文件: {output_path}")
    click.echo(f"模式: {'简单模式' if simple else '完整模式'}")
    click.echo("-" * 50)
    
    # 执行转换
    converter = PDFToExcelConverter()
    
    with click.progressbar(length=100, label='转换进度') as bar:
        bar.update(10)  # 开始
        
        success = converter.convert(input_path, output_path, simple)
        
        bar.update(90)  # 完成
    
    if success:
        click.echo(click.style("✓ 转换成功!", fg='green', bold=True))
        click.echo(f"输出文件: {output_path}")
        
        # 显示文件大小
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            click.echo(f"文件大小: {file_size:,} 字节")
    else:
        click.echo(click.style("✗ 转换失败!", fg='red', bold=True))
        click.echo("请查看日志文件 pdf_to_excel.log 获取详细错误信息")
        sys.exit(1)


def convert_file(pdf_path: str, output_path: str = None, simple_mode: bool = False) -> bool:
    """
    编程接口：转换PDF文件
    
    Args:
        pdf_path: PDF文件路径
        output_path: 输出Excel文件路径
        simple_mode: 是否使用简单模式
        
    Returns:
        转换是否成功
    """
    converter = PDFToExcelConverter()
    
    if not output_path:
        input_name = os.path.splitext(os.path.basename(pdf_path))[0]
        output_path = f"{input_name}_转换结果.xlsx"
    
    return converter.convert(pdf_path, output_path, simple_mode)


if __name__ == "__main__":
    main()
