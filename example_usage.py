#!/usr/bin/env python3
"""
PDF转Excel转换器使用示例
"""

import os
from main import convert_file, PDFToExcelConverter


def example_1_simple_conversion():
    """示例1: 简单转换"""
    print("=== 示例1: 简单转换 ===")
    
    pdf_file = "谷付荣.pdf"
    output_file = "示例1_简单转换.xlsx"
    
    if not os.path.exists(pdf_file):
        print(f"错误: PDF文件不存在 - {pdf_file}")
        return
    
    print(f"转换文件: {pdf_file}")
    print(f"输出文件: {output_file}")
    
    # 使用便捷函数进行转换
    success = convert_file(pdf_file, output_file, simple_mode=True)
    
    if success:
        print("✓ 转换成功!")
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"文件大小: {file_size:,} 字节")
    else:
        print("✗ 转换失败")


def example_2_advanced_conversion():
    """示例2: 高级转换（带格式）"""
    print("\n=== 示例2: 高级转换（带格式） ===")
    
    pdf_file = "谷付荣.pdf"
    output_file = "示例2_高级转换.xlsx"
    
    if not os.path.exists(pdf_file):
        print(f"错误: PDF文件不存在 - {pdf_file}")
        return
    
    print(f"转换文件: {pdf_file}")
    print(f"输出文件: {output_file}")
    
    # 使用转换器类进行高级转换
    converter = PDFToExcelConverter()
    success = converter.convert(pdf_file, output_file, simple_mode=False)
    
    if success:
        print("✓ 转换成功!")
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"文件大小: {file_size:,} 字节")
    else:
        print("✗ 转换失败")


def example_3_batch_conversion():
    """示例3: 批量转换"""
    print("\n=== 示例3: 批量转换 ===")
    
    # 查找当前目录下的所有PDF文件
    pdf_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("当前目录下没有找到PDF文件")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件:")
    for pdf_file in pdf_files:
        print(f"  - {pdf_file}")
    
    # 批量转换
    converter = PDFToExcelConverter()
    success_count = 0
    
    for pdf_file in pdf_files:
        output_file = f"批量转换_{os.path.splitext(pdf_file)[0]}.xlsx"
        print(f"\n转换: {pdf_file} -> {output_file}")
        
        try:
            success = converter.convert(pdf_file, output_file, simple_mode=True)
            if success:
                print("  ✓ 成功")
                success_count += 1
            else:
                print("  ✗ 失败")
        except Exception as e:
            print(f"  ✗ 错误: {e}")
    
    print(f"\n批量转换完成: {success_count}/{len(pdf_files)} 个文件转换成功")


def example_4_custom_processing():
    """示例4: 自定义处理"""
    print("\n=== 示例4: 自定义处理 ===")
    
    pdf_file = "谷付荣.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"错误: PDF文件不存在 - {pdf_file}")
        return
    
    # 导入必要的模块
    import sys
    sys.path.insert(0, 'src')
    
    from pdf_reader import PDFReader
    from data_processor import DataProcessor
    from excel_writer import save_dataframe_to_excel
    
    print(f"自定义处理文件: {pdf_file}")
    
    try:
        with PDFReader(pdf_file) as reader:
            # 获取PDF信息
            pdf_info = reader.get_pdf_info()
            print(f"PDF页数: {pdf_info.get('pages', 0)}")
            
            # 只处理前5页的表格
            tables_info = []
            for page_num in range(min(5, pdf_info.get('pages', 0))):
                page_tables = reader.extract_tables_from_page(page_num)
                for table_index, table_data in enumerate(page_tables):
                    if table_data:
                        table_info = {
                            'page': page_num + 1,
                            'table_index': table_index + 1,
                            'data': table_data,
                            'rows': len(table_data),
                            'cols': len(table_data[0]) if table_data else 0
                        }
                        tables_info.append(table_info)
            
            print(f"前5页找到 {len(tables_info)} 个表格")
            
            # 处理数据
            processor = DataProcessor()
            processed_df = processor.process_all_tables(tables_info)
            
            if not processed_df.empty:
                # 保存结果
                output_file = "示例4_自定义处理.xlsx"
                success = save_dataframe_to_excel(processed_df, output_file, pdf_info, simple_mode=True)
                
                if success:
                    print(f"✓ 自定义处理完成: {output_file}")
                    print(f"数据统计: {len(processed_df)} 行 x {len(processed_df.columns)} 列")
                else:
                    print("✗ 保存失败")
            else:
                print("✗ 没有提取到有效数据")
                
    except Exception as e:
        print(f"自定义处理过程中出错: {e}")


def main():
    """主函数"""
    print("PDF转Excel转换器使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_1_simple_conversion()
    example_2_advanced_conversion()
    example_3_batch_conversion()
    example_4_custom_processing()
    
    print("\n" + "=" * 50)
    print("所有示例运行完成!")


if __name__ == "__main__":
    main()
